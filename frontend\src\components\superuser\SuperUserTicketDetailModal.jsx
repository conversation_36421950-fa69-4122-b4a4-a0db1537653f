import React, { useState } from 'react';
import {
  X,
  Calendar,
  User,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Paperclip,
  Send,
  Download,
  MessageSquare,
  Shield,
  ArrowUp,
  Users,
  FileText,
  Eye,
  Edit
} from 'lucide-react';

const SuperUserTicketDetailModal = ({ ticket, isOpen, onClose, onApprove, onReject }) => {
  const [approvalNotes, setApprovalNotes] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [showRejectForm, setShowRejectForm] = useState(false);
  const [showApproveForm, setShowApproveForm] = useState(false);

  if (!isOpen || !ticket) return null;

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-500';
      case 'High':
        return 'bg-orange-500';
      case 'Medium':
        return 'bg-yellow-500';
      case 'Low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'High':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'Medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'Low':
        return 'text-green-600 bg-green-100 border-green-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  const handleApprove = () => {
    onApprove(ticket.id, approvalNotes);
    setApprovalNotes('');
    setShowApproveForm(false);
    onClose();
  };

  const handleReject = () => {
    if (!rejectionReason.trim()) {
      alert('Please provide a reason for rejection');
      return;
    }
    onReject(ticket.id, rejectionReason);
    setRejectionReason('');
    setShowRejectForm(false);
    onClose();
  };

  const resetForms = () => {
    setShowApproveForm(false);
    setShowRejectForm(false);
    setApprovalNotes('');
    setRejectionReason('');
  };

  const handleClose = () => {
    resetForms();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl w-full max-w-5xl max-h-[95vh] overflow-hidden flex flex-col shadow-2xl">
        {/* Enhanced Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 text-white p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="bg-white bg-opacity-20 rounded-lg p-3">
                <Shield className="w-6 h-6" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">Ticket Review</h2>
                <p className="text-blue-100 text-sm">ID: {ticket.id}</p>
              </div>
            </div>
            <button
              onClick={handleClose}
              className="text-white hover:bg-white hover:bg-opacity-20 rounded-lg p-2 transition-colors"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6">
            {/* Priority and Risk Banner */}
            <div className="flex items-center justify-between mb-6 p-4 bg-gray-50 rounded-lg border">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-2 ${getPriorityColor(ticket.priority)}`}></div>
                  <span className="font-medium text-gray-900">{ticket.priority} Priority</span>
                  {ticket.priority === 'Critical' && (
                    <ArrowUp className="w-4 h-4 ml-1 text-red-500" />
                  )}
                </div>
                <div className={`px-3 py-1 text-sm font-medium rounded-full border ${getRiskColor(ticket.riskLevel)}`}>
                  {ticket.riskLevel} Risk
                </div>
              </div>
              <div className="text-sm text-gray-500">
                Created: {ticket.created}
              </div>
            </div>

            {/* Ticket Information Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              {/* Left Column */}
              <div className="space-y-4">
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <MessageSquare className="w-4 h-4 inline mr-2" />
                    Subject
                  </label>
                  <p className="text-gray-900 font-medium">{ticket.subject}</p>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <User className="w-4 h-4 inline mr-2" />
                    Customer Information
                  </label>
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-10 w-10">
                      <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                    </div>
                    <div className="ml-3">
                      <p className="text-gray-900 font-medium">{ticket.customer}</p>
                      <p className="text-gray-500 text-sm">Customer ID: CUST-{Math.random().toString(36).substr(2, 6).toUpperCase()}</p>
                    </div>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <FileText className="w-4 h-4 inline mr-2" />
                    Category & Attachments
                  </label>
                  <div className="flex items-center justify-between">
                    <span className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                      {ticket.category}
                    </span>
                    {ticket.attachments > 0 && (
                      <div className="flex items-center text-gray-600">
                        <Paperclip className="w-4 h-4 mr-1" />
                        <span className="text-sm">{ticket.attachments} files</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-4">
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <AlertCircle className="w-4 h-4 inline mr-2" />
                    Description
                  </label>
                  <div className="bg-gray-50 rounded-lg p-4 max-h-32 overflow-y-auto">
                    <p className="text-gray-900 text-sm leading-relaxed">{ticket.description}</p>
                  </div>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <label className="block text-sm font-semibold text-gray-700 mb-2">
                    <Calendar className="w-4 h-4 inline mr-2" />
                    Timeline
                  </label>
                  <div className="space-y-2">
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                      <span className="text-gray-600">Created: {ticket.created}</span>
                    </div>
                    <div className="flex items-center text-sm">
                      <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
                      <span className="text-gray-600">Status: Pending Approval</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Action Forms */}
            {showApproveForm && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="text-lg font-semibold text-green-800 mb-3">Approve Ticket</h4>
                <div>
                  <label className="block text-sm font-medium text-green-700 mb-2">
                    Approval Notes (Optional)
                  </label>
                  <textarea
                    value={approvalNotes}
                    onChange={(e) => setApprovalNotes(e.target.value)}
                    rows="3"
                    className="w-full border border-green-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-green-500 resize-none"
                    placeholder="Add any notes for the approval decision..."
                  />
                </div>
              </div>
            )}

            {showRejectForm && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <h4 className="text-lg font-semibold text-red-800 mb-3">Reject Ticket</h4>
                <div>
                  <label className="block text-sm font-medium text-red-700 mb-2">
                    Rejection Reason *
                  </label>
                  <textarea
                    value={rejectionReason}
                    onChange={(e) => setRejectionReason(e.target.value)}
                    rows="3"
                    className="w-full border border-red-300 rounded-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500 resize-none"
                    placeholder="Please provide a clear reason for rejection..."
                    required
                  />
                  <p className="text-xs text-red-600 mt-1">This reason will be sent to the customer</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Footer */}
        <div className="border-t border-gray-200 bg-gray-50 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Review this ticket carefully before making a decision
            </div>
            <div className="flex space-x-3">
              {!showApproveForm && !showRejectForm && (
                <>
                  <button
                    onClick={() => setShowRejectForm(true)}
                    className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-sm"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Reject
                  </button>
                  <button
                    onClick={() => setShowApproveForm(true)}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors shadow-sm"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Approve
                  </button>
                </>
              )}

              {showApproveForm && (
                <>
                  <button
                    onClick={resetForms}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleApprove}
                    className="flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors shadow-sm"
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Confirm Approval
                  </button>
                </>
              )}

              {showRejectForm && (
                <>
                  <button
                    onClick={resetForms}
                    className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleReject}
                    className="flex items-center px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors shadow-sm"
                  >
                    <XCircle className="w-4 h-4 mr-2" />
                    Confirm Rejection
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperUserTicketDetailModal;
