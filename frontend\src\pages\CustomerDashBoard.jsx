import { Plus } from "lucide-react";
import { useState } from "react";
import CreateTicketModal from "../components/CreateTicketModal";
import TicketDetailModal from "../components/TicketDetailModal";
import TicketFilters from "../components/TicketFilters";
import TicketTable from "../components/TicketTable";

const CustomerDashboard = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");

  // Sample ticket data with comprehensive status and capabilities
  const tickets = [
    {
      id: "TK-001",
      subject: "Login Issues",
      category: "Technical",
      status: "In Progress",
      priority: "High",
      created: "2025-05-25 09:30 AM",
      createdTimestamp: new Date("2025-05-25T09:30:00"),
      lastUpdate: "2 hours ago",
      agent: "<PERSON>",
      agentId: "agent_001",
      canDelete: false,
      canClose: true,
      canReopen: false,
      canResubmit: false,
      seenByAgent: true,
      description:
        "I'm experiencing login issues with my account. When I try to log in, I get an error message saying 'Invalid credentials' even though I'm using the correct username and password. This started happening after the recent system update.",
      rejectionReason: null,
      approvalNotes: "Approved for technical investigation",
      isActive: true,
    },
    {
      id: "TK-002",
      subject: "Feature Request - Dark Mode",
      category: "Feature Request",
      status: "Pending Approval",
      priority: "Medium",
      created: "2025-05-26 02:15 PM",
      createdTimestamp: new Date("2025-05-26T14:15:00"),
      lastUpdate: "5 minutes ago",
      agent: null,
      agentId: null,
      canDelete: true, // Can delete within 10 minutes if not seen
      canClose: false,
      canReopen: false,
      canResubmit: false,
      seenByAgent: false,
      description:
        "It would be great to have a dark mode option in the application. Many users prefer dark themes, especially when working in low-light environments. This would improve user experience and reduce eye strain.",
      rejectionReason: null,
      approvalNotes: null,
      isActive: false,
    },
    {
      id: "TK-003",
      subject: "Billing Question",
      category: "Billing",
      status: "Resolved",
      priority: "Low",
      created: "2025-05-20 10:00 AM",
      createdTimestamp: new Date("2025-05-20T10:00:00"),
      lastUpdate: "3 days ago",
      agent: "Sarah Johnson",
      agentId: "agent_002",
      canDelete: false,
      canClose: false,
      canReopen: true, // Can continue chat after resolution
      canResubmit: false,
      seenByAgent: true,
      description:
        "I have a question about my recent billing statement. There seems to be a charge that I don't recognize. Could you please help me understand what this charge is for?",
      rejectionReason: null,
      approvalNotes: "Standard billing inquiry - approved",
      isActive: false,
    },
    {
      id: "TK-004",
      subject: "API Documentation",
      category: "Documentation",
      status: "Rejected",
      priority: "Medium",
      created: "2025-05-24 11:45 AM",
      createdTimestamp: new Date("2025-05-24T11:45:00"),
      lastUpdate: "1 day ago",
      agent: null,
      agentId: null,
      canDelete: false,
      canClose: false,
      canReopen: false,
      canResubmit: true, // Can modify and resubmit
      seenByAgent: true,
      description:
        "The API documentation seems to be missing some important endpoints. Specifically, I can't find documentation for the user management endpoints that were mentioned in the changelog.",
      rejectionReason:
        "Please be more specific about which endpoints are missing. Include the exact endpoint names and expected functionality.",
      approvalNotes: null,
      isActive: false,
    },
    {
      id: "TK-005",
      subject: "Account Security Concern",
      category: "Security",
      status: "Approved",
      priority: "High",
      created: "2025-05-26 08:20 AM",
      createdTimestamp: new Date("2025-05-26T08:20:00"),
      lastUpdate: "30 minutes ago",
      agent: "Mike Wilson",
      agentId: "agent_003",
      canDelete: false,
      canClose: true,
      canReopen: false,
      canResubmit: false,
      seenByAgent: true,
      description:
        "I noticed some unusual login attempts on my account from unknown IP addresses. I'm concerned about the security of my account and would like to review recent activity.",
      rejectionReason: null,
      approvalNotes: "Security concern - escalated to security team",
      isActive: true,
    },
    {
      id: "TK-006",
      subject: "Mobile App Crash",
      category: "Technical",
      status: "Seen",
      priority: "Medium",
      created: "2025-05-26 01:00 PM",
      createdTimestamp: new Date("2025-05-26T13:00:00"),
      lastUpdate: "15 minutes ago",
      agent: "John Smith",
      agentId: "agent_001",
      canDelete: false,
      canClose: true,
      canReopen: false,
      canResubmit: false,
      seenByAgent: true,
      description:
        "The mobile app keeps crashing when I try to access the dashboard. This happens consistently on both iOS and Android devices.",
      rejectionReason: null,
      approvalNotes: "Technical issue confirmed - investigating",
      isActive: true,
    },
  ];

  // Handler functions
  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setShowDetailModal(true);
  };

  const handleTicketAction = (action, ticket) => {
    switch (action) {
      case "reopen":
        console.log("Reopening ticket (Continue Chat):", ticket.id);
        // Change status back to "In Progress" and allow new comments
        break;
      case "delete": {
        // Check if ticket can be deleted (within 10 minutes and not seen by super user)
        const now = new Date();
        const timeDiff = (now - ticket.createdTimestamp) / (1000 * 60); // minutes
        if (timeDiff <= 10 && !ticket.seenByAgent) {
          if (window.confirm("Are you sure you want to delete this ticket?")) {
            console.log("Deleting ticket:", ticket.id);
            // Implement delete logic
          }
        } else {
          alert(
            "This ticket cannot be deleted. It's either been seen by an agent or the 10-minute window has passed."
          );
        }
        break;
      }
      case "close":
        if (window.confirm("Are you sure you want to close this ticket?")) {
          console.log("Closing ticket:", ticket.id);
          // Change status to "Closed" - customer initiated closure
        }
        break;
      case "resubmit":
        console.log("Resubmitting rejected ticket:", ticket.id);
        // Open edit modal for rejected ticket
        break;
      default:
        break;
    }
  };

  const handleCreateTicket = async (ticketData) => {
    console.log("Creating new ticket:", ticketData);
    // Create ticket with "Pending Approval" status
    // Max 25MB attachments, max 5 files
  };

  const filteredTickets = tickets.filter((ticket) => {
    const matchesSearch =
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.category.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesTab =
      activeTab === "all" ||
      (activeTab === "pending" && ticket.status === "Pending Approval") ||
      (activeTab === "active" &&
        ["In Progress", "Seen", "Approved"].includes(ticket.status)) ||
      (activeTab === "resolved" && ticket.status === "Resolved") ||
      (activeTab === "rejected" && ticket.status === "Rejected");

    return matchesSearch && matchesTab;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                My Support Tickets
              </h1>
              <p className="text-sm text-gray-600">
                Track and manage your support requests
              </p>
            </div>
            <button
              onClick={() => setShowCreateModal(true)}
              className="flex items-center px-3 py-1.5 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <Plus className="w-3 h-3 mr-1" />
              New Ticket
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        <TicketFilters
          activeTab={activeTab}
          setActiveTab={setActiveTab}
          searchTerm={searchTerm}
          setSearchTerm={setSearchTerm}
          tickets={tickets}
        />

        {/* Tickets List */}
        <TicketTable
          tickets={filteredTickets}
          onTicketClick={handleTicketClick}
          onTicketAction={handleTicketAction}
        />
      </div>

      {/* Modals */}
      <CreateTicketModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSubmit={handleCreateTicket}
      />

      <TicketDetailModal
        ticket={selectedTicket}
        isOpen={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        onTicketAction={handleTicketAction}
      />
    </div>
  );
};

export default CustomerDashboard;
