import {
  ArrowUp,
  Calendar,
  CheckCircle,
  Eye,
  Filter,
  MessageSquare,
  Search,
  Users,
  XCircle,
} from "lucide-react";
import { useState } from "react";

const SuperUserDashboard = () => {
  const [selectedTickets, setSelectedTickets] = useState([]);
  const [activeFilter, setActiveFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);

  // Sample pending tickets data
  const pendingTickets = [
    {
      id: "TK-005",
      subject: "API Rate Limiting Issues",
      customer: "TechCorp Ltd",
      category: "Technical",
      priority: "High",
      created: "2025-05-26 09:30",
      description: "Our API calls are being rate limited unexpectedly...",
      attachments: 2,
      riskLevel: "Medium",
    },
    {
      id: "TK-006",
      subject: "Bulk Data Export Feature",
      customer: "DataSync Inc",
      category: "Feature Request",
      priority: "Medium",
      created: "2025-05-26 10:15",
      description: "Need ability to export large datasets in CSV format...",
      attachments: 1,
      riskLevel: "Low",
    },
    {
      id: "TK-007",
      subject: "Security Vulnerability Report",
      customer: "SecureApp Solutions",
      category: "Security",
      priority: "Critical",
      created: "2025-05-26 11:00",
      description: "Potential XSS vulnerability found in user dashboard...",
      attachments: 3,
      riskLevel: "High",
    },
    {
      id: "TK-008",
      subject: "Integration Documentation",
      customer: "DevTeam Alpha",
      category: "Documentation",
      priority: "Low",
      created: "2025-05-26 11:45",
      description: "Missing documentation for webhook integration...",
      attachments: 0,
      riskLevel: "Low",
    },
  ];

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "Critical":
        return "bg-red-500";
      case "High":
        return "bg-orange-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case "High":
        return "text-red-600 bg-red-100";
      case "Medium":
        return "text-yellow-600 bg-yellow-100";
      case "Low":
        return "text-green-600 bg-green-100";
      default:
        return "text-gray-600 bg-gray-100";
    }
  };

  const handleTicketSelection = (ticketId) => {
    setSelectedTickets((prev) =>
      prev.includes(ticketId)
        ? prev.filter((id) => id !== ticketId)
        : [...prev, ticketId]
    );
  };

  const handleBulkApprove = () => {
    console.log("Bulk approving tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  const handleBulkReject = () => {
    console.log("Bulk rejecting tickets:", selectedTickets);
    setSelectedTickets([]);
  };

  const handleTicketClick = (ticket) => {
    setSelectedTicket(ticket);
    setShowApprovalModal(true);
  };

  const handleApprove = (ticketId, approvalNotes) => {
    console.log("Approving ticket:", ticketId, "with notes:", approvalNotes);
    // Here you would typically make an API call to approve the ticket
    // Update ticket status to "Approved" and add approval notes
  };

  const handleReject = (ticketId, rejectionReason) => {
    console.log("Rejecting ticket:", ticketId, "with reason:", rejectionReason);
    // Here you would typically make an API call to reject the ticket
    // Update ticket status to "Rejected" and add rejection reason
  };

  const filteredTickets = pendingTickets.filter((ticket) => {
    const matchesSearch =
      ticket.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
      ticket.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter =
      activeFilter === "all" ||
      (activeFilter === "high-priority" &&
        ["Critical", "High"].includes(ticket.priority)) ||
      (activeFilter === "technical" && ticket.category === "Technical") ||
      (activeFilter === "security" && ticket.category === "Security");
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-3">
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Ticket Approval Center
              </h1>
              <p className="text-sm text-gray-600">
                Review and approve customer support requests
              </p>
            </div>
            <div className="flex space-x-2">
              {selectedTickets.length > 0 && (
                <>
                  <button
                    onClick={handleBulkApprove}
                    className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700"
                  >
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Approve ({selectedTickets.length})
                  </button>
                  <button
                    onClick={handleBulkReject}
                    className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700"
                  >
                    <XCircle className="w-3 h-3 mr-1" />
                    Reject ({selectedTickets.length})
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
        {/* Quick Actions and Filters */}
        <div className="bg-white rounded-lg shadow-sm mb-3">
          <div className="p-3">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
              <div className="flex flex-wrap gap-1">
                {[
                  { key: "all", label: "All Pending", count: 4 },
                  { key: "high-priority", label: "High Priority", count: 2 },
                  { key: "technical", label: "Technical", count: 1 },
                  { key: "security", label: "Security", count: 1 },
                ].map((filter) => (
                  <button
                    key={filter.key}
                    onClick={() => setActiveFilter(filter.key)}
                    className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                      activeFilter === filter.key
                        ? "bg-blue-100 text-blue-700"
                        : "text-gray-600 hover:text-gray-900 hover:bg-gray-100"
                    }`}
                  >
                    {filter.label} ({filter.count})
                  </button>
                ))}
              </div>
              <div className="flex space-x-2">
                <div className="relative">
                  <Search className="w-3 h-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search tickets..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                <button className="flex items-center px-2 py-1.5 text-xs border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
                  <Filter className="w-3 h-3 mr-1" />
                  More Filters
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Pending Tickets Table */}
        <div className="bg-white rounded-lg shadow-sm">
          <div className="px-3 py-2 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-medium text-gray-900">
                Tickets Pending Approval
              </h3>
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedTickets(filteredTickets.map((t) => t.id));
                    } else {
                      setSelectedTickets([]);
                    }
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <label className="text-xs text-gray-600">Select All</label>
              </div>
            </div>
          </div>
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Select
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ticket Details
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Customer
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Risk Level
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTickets.map((ticket) => (
                  <tr
                    key={ticket.id}
                    className="hover:bg-blue-50 cursor-pointer transition-colors"
                    onClick={(e) => {
                      // Don't trigger row click if clicking on checkbox or action buttons
                      if (
                        e.target.type !== "checkbox" &&
                        !e.target.closest("button")
                      ) {
                        handleTicketClick(ticket);
                      }
                    }}
                  >
                    <td className="px-2 py-2 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedTickets.includes(ticket.id)}
                        onChange={() => handleTicketSelection(ticket.id)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                        onClick={(e) => e.stopPropagation()}
                      />
                    </td>
                    <td className="px-2 py-2">
                      <div>
                        <div className="text-xs font-medium text-gray-900">
                          {ticket.id}
                        </div>
                        <div className="text-xs text-gray-600">
                          {ticket.subject}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center mt-0.5">
                          <MessageSquare className="w-2 h-2 mr-1" />
                          {ticket.category}
                          {ticket.attachments > 0 && (
                            <span className="ml-1 flex items-center">
                              📎 {ticket.attachments}
                            </span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-6 w-6">
                          <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
                            <Users className="w-3 h-3 text-gray-600" />
                          </div>
                        </div>
                        <div className="ml-2">
                          <div className="text-xs font-medium text-gray-900">
                            {ticket.customer}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <div className="flex items-center">
                        <div
                          className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
                            ticket.priority
                          )}`}
                        ></div>
                        <span className="text-xs text-gray-900">
                          {ticket.priority}
                        </span>
                        {ticket.priority === "Critical" && (
                          <ArrowUp className="w-3 h-3 ml-1 text-red-500" />
                        )}
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap">
                      <span
                        className={`px-1.5 py-0.5 text-xs font-medium rounded-full ${getRiskColor(
                          ticket.riskLevel
                        )}`}
                      >
                        {ticket.riskLevel}
                      </span>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="w-3 h-3 mr-1" />
                        {ticket.created}
                      </div>
                    </td>
                    <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
                      <div className="flex space-x-1">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleTicketClick(ticket);
                          }}
                          className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100 transition-colors"
                          title="View Details"
                        >
                          <Eye className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleApprove(ticket.id, "");
                          }}
                          className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100 transition-colors"
                          title="Quick Approve"
                        >
                          <CheckCircle className="w-3 h-3" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            // For quick reject, we'll need a reason, so open the modal
                            handleTicketClick(ticket);
                          }}
                          className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition-colors"
                          title="Reject (with reason)"
                        >
                          <XCircle className="w-3 h-3" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Approval Modal */}
      {showApprovalModal && <ApprovalModal />}
    </div>
  );
};

export default SuperUserDashboard;
