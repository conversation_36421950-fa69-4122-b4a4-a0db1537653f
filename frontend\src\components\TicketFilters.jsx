import React from 'react';
import { Search, Filter } from 'lucide-react';

const TicketFilters = ({ 
  activeTab, 
  setActiveTab, 
  searchTerm, 
  setSearchTerm, 
  tickets 
}) => {
  const getTabCounts = () => {
    return {
      all: tickets.length,
      pending: tickets.filter(t => t.status === 'Pending Approval').length,
      active: tickets.filter(t => ['In Progress', 'Seen'].includes(t.status)).length,
      resolved: tickets.filter(t => t.status === 'Resolved').length,
      rejected: tickets.filter(t => t.status === 'Rejected').length
    };
  };

  const tabCounts = getTabCounts();

  const tabs = [
    { key: 'all', label: 'All Tickets', count: tabCounts.all },
    { key: 'pending', label: 'Pending Approval', count: tabCounts.pending },
    { key: 'active', label: 'Active', count: tabCounts.active },
    { key: 'resolved', label: 'Resolved', count: tabCounts.resolved },
    { key: 'rejected', label: 'Rejected', count: tabCounts.rejected }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm mb-3">
      <div className="p-3">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
          <div className="flex space-x-1 flex-wrap">
            {tabs.map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                  activeTab === tab.key
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                }`}
              >
                {tab.label} ({tab.count})
              </button>
            ))}
          </div>
          <div className="flex space-x-2">
            <div className="relative">
              <Search className="w-3 h-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search tickets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <button className="flex items-center px-2 py-1.5 text-xs border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50">
              <Filter className="w-3 h-3 mr-1" />
              Filter
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TicketFilters;
