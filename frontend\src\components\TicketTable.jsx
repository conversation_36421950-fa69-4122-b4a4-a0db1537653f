import {
  <PERSON>ertCircle,
  CheckCircle,
  Clock,
  Edit,
  Eye,
  RotateCcw,
  Trash2,
  XCircle,
} from "lucide-react";

const TicketTable = ({ tickets, onTicketClick, onTicketAction }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case "Pending Approval":
        return <Clock className="w-3 h-3 text-yellow-500" />;
      case "In Progress":
        return <AlertCircle className="w-3 h-3 text-blue-500" />;
      case "Resolved":
        return <CheckCircle className="w-3 h-3 text-green-500" />;
      case "Rejected":
        return <XCircle className="w-3 h-3 text-red-500" />;
      default:
        return <Clock className="w-3 h-3 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case "Pending Approval":
        return "bg-yellow-100 text-yellow-800";
      case "In Progress":
        return "bg-blue-100 text-blue-800";
      case "Resolved":
        return "bg-green-100 text-green-800";
      case "Rejected":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case "High":
        return "bg-red-500";
      case "Medium":
        return "bg-yellow-500";
      case "Low":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleTicketClick = (ticket, e) => {
    // Don't trigger if clicking on action buttons
    if (e.target.closest("button")) {
      return;
    }
    onTicketClick(ticket);
  };

  if (tickets.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <div className="text-gray-500">
          <Clock className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-lg font-medium">No tickets found</p>
          <p className="text-sm">
            Try adjusting your search or filter criteria
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticket
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Agent
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Last Update
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {tickets.map((ticket) => (
              <tr
                key={ticket.id}
                className="hover:bg-gray-50 cursor-pointer"
                onClick={(e) => handleTicketClick(ticket, e)}
              >
                <td className="px-2 py-2 whitespace-nowrap">
                  <div>
                    <div className="text-xs font-medium text-gray-900">
                      {ticket.id}
                    </div>
                    <div className="text-xs text-gray-500">
                      {ticket.subject}
                    </div>
                    <div className="text-xs text-gray-400">
                      {ticket.category}
                    </div>
                  </div>
                </td>
                <td className="px-2 py-2 whitespace-nowrap">
                  <div className="flex items-center">
                    {getStatusIcon(ticket.status)}
                    <span
                      className={`ml-1 px-1.5 py-0.5 text-xs font-medium rounded-full ${getStatusColor(
                        ticket.status
                      )}`}
                    >
                      {ticket.status}
                    </span>
                  </div>
                </td>
                <td className="px-2 py-2 whitespace-nowrap">
                  <div className="flex items-center">
                    <div
                      className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
                        ticket.priority
                      )}`}
                    ></div>
                    <span className="text-xs text-gray-900">
                      {ticket.priority}
                    </span>
                  </div>
                </td>
                <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-900">
                  {ticket.agent || "Unassigned"}
                </td>
                <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
                  {ticket.lastUpdate}
                </td>
                <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
                  <div className="flex space-x-1">
                    <button
                      className="text-blue-600 hover:text-blue-900 p-1"
                      title="View Details & Chat"
                      onClick={(e) => {
                        e.stopPropagation();
                        onTicketClick(ticket);
                      }}
                    >
                      <Eye className="w-3 h-3" />
                    </button>

                    {/* Continue Chat for resolved tickets */}
                    {ticket.canReopen && ticket.status === "Resolved" && (
                      <button
                        className="text-green-600 hover:text-green-900 p-1"
                        title="Continue Chat"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTicketAction("reopen", ticket);
                        }}
                      >
                        <RotateCcw className="w-3 h-3" />
                      </button>
                    )}

                    {/* Close ticket for active tickets */}
                    {ticket.canClose &&
                      ["In Progress", "Approved", "Seen"].includes(
                        ticket.status
                      ) && (
                        <button
                          className="text-gray-600 hover:text-gray-900 p-1"
                          title="Close Ticket"
                          onClick={(e) => {
                            e.stopPropagation();
                            onTicketAction("close", ticket);
                          }}
                        >
                          <XCircle className="w-3 h-3" />
                        </button>
                      )}

                    {/* Resubmit for rejected tickets */}
                    {ticket.canResubmit && ticket.status === "Rejected" && (
                      <button
                        className="text-blue-600 hover:text-blue-900 p-1"
                        title="Resubmit Ticket"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTicketAction("resubmit", ticket);
                        }}
                      >
                        <Edit className="w-3 h-3" />
                      </button>
                    )}

                    {/* Delete for eligible tickets */}
                    {ticket.canDelete && (
                      <button
                        className="text-red-600 hover:text-red-900 p-1"
                        title="Delete Ticket (within 10 min)"
                        onClick={(e) => {
                          e.stopPropagation();
                          onTicketAction("delete", ticket);
                        }}
                      >
                        <Trash2 className="w-3 h-3" />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TicketTable;
