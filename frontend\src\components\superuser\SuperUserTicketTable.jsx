import React from 'react';
import {
  ArrowUp,
  Calendar,
  CheckCircle,
  Eye,
  MessageSquare,
  Users,
  XCircle,
} from 'lucide-react';

const SuperUserTicketTable = ({ 
  tickets, 
  selectedTickets, 
  onTicketSelection, 
  onTicketClick, 
  onQuickApprove, 
  onSelectAll 
}) => {
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'Critical':
        return 'bg-red-500';
      case 'High':
        return 'bg-orange-500';
      case 'Medium':
        return 'bg-yellow-500';
      case 'Low':
        return 'bg-green-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getRiskColor = (risk) => {
    switch (risk) {
      case 'High':
        return 'text-red-600 bg-red-100';
      case 'Medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'Low':
        return 'text-green-600 bg-green-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  if (tickets.length === 0) {
    return (
      <div className="bg-white rounded-lg shadow-sm p-8 text-center">
        <div className="text-gray-500">
          <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p className="text-lg font-medium">No pending tickets</p>
          <p className="text-sm">All tickets have been reviewed</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-sm">
      <div className="px-3 py-2 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-base font-medium text-gray-900">
            Tickets Pending Approval ({tickets.length})
          </h3>
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              onChange={onSelectAll}
              checked={selectedTickets.length === tickets.length && tickets.length > 0}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <label className="text-xs text-gray-600">Select All</label>
          </div>
        </div>
      </div>
      
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Select
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticket Details
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Priority
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Risk Level
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th className="px-2 py-1.5 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          
          <tbody className="bg-white divide-y divide-gray-200">
            {tickets.map((ticket) => (
              <tr
                key={ticket.id}
                className="hover:bg-blue-50 cursor-pointer transition-colors"
                onClick={(e) => {
                  // Don't trigger row click if clicking on checkbox or action buttons
                  if (
                    e.target.type !== 'checkbox' &&
                    !e.target.closest('button')
                  ) {
                    onTicketClick(ticket);
                  }
                }}
              >
                <td className="px-2 py-2 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={selectedTickets.includes(ticket.id)}
                    onChange={() => onTicketSelection(ticket.id)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    onClick={(e) => e.stopPropagation()}
                  />
                </td>
                
                <td className="px-2 py-2">
                  <div>
                    <div className="text-xs font-medium text-gray-900">
                      {ticket.id}
                    </div>
                    <div className="text-xs text-gray-600 truncate max-w-xs">
                      {ticket.subject}
                    </div>
                    <div className="text-xs text-gray-400 flex items-center mt-0.5">
                      <MessageSquare className="w-2 h-2 mr-1" />
                      {ticket.category}
                      {ticket.attachments > 0 && (
                        <span className="ml-1 flex items-center">
                          📎 {ticket.attachments}
                        </span>
                      )}
                    </div>
                  </div>
                </td>
                
                <td className="px-2 py-2 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-6 w-6">
                      <div className="h-6 w-6 rounded-full bg-gray-300 flex items-center justify-center">
                        <Users className="w-3 h-3 text-gray-600" />
                      </div>
                    </div>
                    <div className="ml-2">
                      <div className="text-xs font-medium text-gray-900 truncate max-w-24">
                        {ticket.customer}
                      </div>
                    </div>
                  </div>
                </td>
                
                <td className="px-2 py-2 whitespace-nowrap">
                  <div className="flex items-center">
                    <div
                      className={`w-1.5 h-1.5 rounded-full mr-1 ${getPriorityColor(
                        ticket.priority
                      )}`}
                    ></div>
                    <span className="text-xs text-gray-900">
                      {ticket.priority}
                    </span>
                    {ticket.priority === 'Critical' && (
                      <ArrowUp className="w-3 h-3 ml-1 text-red-500" />
                    )}
                  </div>
                </td>
                
                <td className="px-2 py-2 whitespace-nowrap">
                  <span
                    className={`px-1.5 py-0.5 text-xs font-medium rounded-full ${getRiskColor(
                      ticket.riskLevel
                    )}`}
                  >
                    {ticket.riskLevel}
                  </span>
                </td>
                
                <td className="px-2 py-2 whitespace-nowrap text-xs text-gray-500">
                  <div className="flex items-center">
                    <Calendar className="w-3 h-3 mr-1" />
                    {ticket.created}
                  </div>
                </td>
                
                <td className="px-2 py-2 whitespace-nowrap text-xs font-medium">
                  <div className="flex space-x-1">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onTicketClick(ticket);
                      }}
                      className="text-blue-600 hover:text-blue-900 p-1 rounded hover:bg-blue-100 transition-colors"
                      title="View Details"
                    >
                      <Eye className="w-3 h-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onQuickApprove(ticket.id, '');
                      }}
                      className="text-green-600 hover:text-green-900 p-1 rounded hover:bg-green-100 transition-colors"
                      title="Quick Approve"
                    >
                      <CheckCircle className="w-3 h-3" />
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        // For reject, we need a reason, so open the modal
                        onTicketClick(ticket);
                      }}
                      className="text-red-600 hover:text-red-900 p-1 rounded hover:bg-red-100 transition-colors"
                      title="Reject (with reason)"
                    >
                      <XCircle className="w-3 h-3" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default SuperUserTicketTable;
