import React from 'react';
import { Search, Filter } from 'lucide-react';

const SuperUserFilters = ({ 
  activeFilter, 
  setActiveFilter, 
  searchTerm, 
  setSearchTerm, 
  tickets 
}) => {
  const getFilterCounts = () => {
    return {
      all: tickets.length,
      'high-priority': tickets.filter(t => ['Critical', 'High'].includes(t.priority)).length,
      technical: tickets.filter(t => t.category === 'Technical').length,
      security: tickets.filter(t => t.category === 'Security').length,
      'feature-request': tickets.filter(t => t.category === 'Feature Request').length,
      documentation: tickets.filter(t => t.category === 'Documentation').length,
      billing: tickets.filter(t => t.category === 'Billing').length
    };
  };

  const filterCounts = getFilterCounts();

  const filters = [
    { key: 'all', label: 'All Pending', count: filterCounts.all },
    { key: 'high-priority', label: 'High Priority', count: filterCounts['high-priority'] },
    { key: 'technical', label: 'Technical', count: filterCounts.technical },
    { key: 'security', label: 'Security', count: filterCounts.security },
    { key: 'feature-request', label: 'Features', count: filterCounts['feature-request'] },
    { key: 'documentation', label: 'Docs', count: filterCounts.documentation },
    { key: 'billing', label: 'Billing', count: filterCounts.billing }
  ];

  return (
    <div className="bg-white rounded-lg shadow-sm mb-3">
      <div className="p-3">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-2 sm:space-y-0">
          {/* Filter Tabs */}
          <div className="flex flex-wrap gap-1">
            {filters.map((filter) => (
              <button
                key={filter.key}
                onClick={() => setActiveFilter(filter.key)}
                className={`px-3 py-1.5 rounded-md text-xs font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-blue-100 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100 border border-transparent'
                }`}
              >
                {filter.label} ({filter.count})
              </button>
            ))}
          </div>
          
          {/* Search and Additional Filters */}
          <div className="flex space-x-2">
            <div className="relative">
              <Search className="w-3 h-3 absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search tickets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
            <button className="flex items-center px-2 py-1.5 text-xs border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors">
              <Filter className="w-3 h-3 mr-1" />
              More Filters
            </button>
          </div>
        </div>
        
        {/* Quick Stats */}
        <div className="mt-3 pt-3 border-t border-gray-100">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex space-x-4">
              <span>Total: {filterCounts.all}</span>
              <span className="text-red-600">Critical: {tickets.filter(t => t.priority === 'Critical').length}</span>
              <span className="text-orange-600">High Risk: {tickets.filter(t => t.riskLevel === 'High').length}</span>
            </div>
            <div>
              Showing {tickets.length} tickets
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SuperUserFilters;
