import React from 'react';
import { Check<PERSON>ir<PERSON>, XCircle, Shield, AlertTriangle } from 'lucide-react';

const SuperUserHeader = ({ 
  selectedTickets, 
  onBulkApprove, 
  onBulkReject,
  totalPendingTickets,
  criticalTickets,
  highRiskTickets 
}) => {
  return (
    <div className="bg-white shadow-sm border-b">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center py-3">
          {/* Title and Description */}
          <div className="flex items-center space-x-4">
            <div className="bg-blue-100 rounded-lg p-2">
              <Shield className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <h1 className="text-xl font-bold text-gray-900">
                Ticket Approval Center
              </h1>
              <p className="text-sm text-gray-600">
                Review and approve customer support requests
              </p>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="hidden md:flex items-center space-x-6 text-sm">
            <div className="text-center">
              <div className="text-lg font-bold text-gray-900">{totalPendingTickets}</div>
              <div className="text-xs text-gray-500">Pending</div>
            </div>
            {criticalTickets > 0 && (
              <div className="text-center">
                <div className="text-lg font-bold text-red-600">{criticalTickets}</div>
                <div className="text-xs text-red-500">Critical</div>
              </div>
            )}
            {highRiskTickets > 0 && (
              <div className="text-center">
                <div className="text-lg font-bold text-orange-600">{highRiskTickets}</div>
                <div className="text-xs text-orange-500">High Risk</div>
              </div>
            )}
          </div>

          {/* Bulk Actions */}
          <div className="flex space-x-2">
            {selectedTickets.length > 0 && (
              <>
                <button
                  onClick={onBulkApprove}
                  className="flex items-center px-3 py-1.5 text-sm bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors shadow-sm"
                >
                  <CheckCircle className="w-3 h-3 mr-1" />
                  Approve ({selectedTickets.length})
                </button>
                <button
                  onClick={onBulkReject}
                  className="flex items-center px-3 py-1.5 text-sm bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors shadow-sm"
                >
                  <XCircle className="w-3 h-3 mr-1" />
                  Reject ({selectedTickets.length})
                </button>
              </>
            )}
            
            {selectedTickets.length === 0 && (
              <div className="text-xs text-gray-500 py-1.5 px-3">
                Select tickets for bulk actions
              </div>
            )}
          </div>
        </div>

        {/* Alert Banner for Critical/High Risk Tickets */}
        {(criticalTickets > 0 || highRiskTickets > 0) && (
          <div className="pb-3">
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-3">
              <div className="flex items-center">
                <AlertTriangle className="w-4 h-4 text-orange-600 mr-2" />
                <div className="text-sm">
                  <span className="font-medium text-orange-800">
                    Attention Required: 
                  </span>
                  <span className="text-orange-700 ml-1">
                    {criticalTickets > 0 && `${criticalTickets} critical priority`}
                    {criticalTickets > 0 && highRiskTickets > 0 && ' and '}
                    {highRiskTickets > 0 && `${highRiskTickets} high risk`}
                    {' '}ticket{(criticalTickets + highRiskTickets) > 1 ? 's' : ''} pending review
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SuperUserHeader;
