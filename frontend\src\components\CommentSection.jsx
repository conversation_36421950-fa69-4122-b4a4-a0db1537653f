import React, { useState, useEffect } from 'react';
import {
  MessageSquare,
  User,
  Shield,
  Calendar,
  Paperclip,
  Download,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

const CommentSection = ({ ticketId, canComment, ticketStatus }) => {
  const [comments, setComments] = useState([]);
  const [expandedComments, setExpandedComments] = useState(new Set());

  // Sample comments data - replace with actual API call
  useEffect(() => {
    const sampleComments = [
      {
        id: 1,
        author: '<PERSON>',
        role: 'Agent',
        content: 'Thank you for reporting this issue. I\'ve reviewed your login problem and identified the root cause. It appears to be related to a recent security update.',
        timestamp: '2025-05-25 10:30 AM',
        attachments: [
          {
            id: 1,
            name: 'login_debug_log.txt',
            size: '2.3 KB',
            type: 'text/plain'
          }
        ]
      },
      {
        id: 2,
        author: 'Customer',
        role: 'Customer',
        content: 'Thanks for the quick response! I tried the suggested solution but I\'m still experiencing the same issue. Could you please provide additional guidance?',
        timestamp: '2025-05-25 02:15 PM',
        attachments: [
          {
            id: 2,
            name: 'screenshot_error.png',
            size: '1.2 MB',
            type: 'image/png'
          },
          {
            id: 3,
            name: 'browser_console.txt',
            size: '5.7 KB',
            type: 'text/plain'
          }
        ]
      },
      {
        id: 3,
        author: 'John Smith',
        role: 'Agent',
        content: 'I see the issue now. Let me escalate this to our development team. In the meantime, please try clearing your browser cache and cookies, then attempt to log in again.',
        timestamp: '2025-05-25 04:45 PM',
        attachments: []
      }
    ];
    setComments(sampleComments);
  }, [ticketId]);

  const toggleCommentExpansion = (commentId) => {
    const newExpanded = new Set(expandedComments);
    if (newExpanded.has(commentId)) {
      newExpanded.delete(commentId);
    } else {
      newExpanded.add(commentId);
    }
    setExpandedComments(newExpanded);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (type) => {
    if (type.startsWith('image/')) return '🖼️';
    if (type.includes('pdf')) return '📄';
    if (type.includes('text')) return '📝';
    if (type.includes('zip') || type.includes('rar')) return '📦';
    return '📎';
  };

  const downloadAttachment = (attachment) => {
    // Implement download logic here
    console.log('Downloading attachment:', attachment);
  };

  if (comments.length === 0) {
    return (
      <div className="mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <MessageSquare className="w-5 h-5 mr-2" />
          Comments & Updates
        </h3>
        <div className="text-center py-8 text-gray-500">
          <MessageSquare className="w-12 h-12 mx-auto mb-3 text-gray-300" />
          <p>No comments yet. {canComment ? 'Be the first to add a comment!' : 'Comments will appear here once the ticket is approved.'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6">
      <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
        <MessageSquare className="w-5 h-5 mr-2" />
        Comments & Updates ({comments.length})
      </h3>
      
      <div className="space-y-4">
        {comments.map((comment) => {
          const isExpanded = expandedComments.has(comment.id);
          const hasLongContent = comment.content.length > 200;
          const displayContent = hasLongContent && !isExpanded 
            ? comment.content.substring(0, 200) + '...' 
            : comment.content;

          return (
            <div key={comment.id} className="bg-white border border-gray-200 rounded-lg p-4">
              {/* Comment Header */}
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center space-x-2">
                    {comment.role === 'Agent' ? (
                      <Shield className="w-4 h-4 text-blue-600" />
                    ) : (
                      <User className="w-4 h-4 text-gray-600" />
                    )}
                    <span className="font-medium text-gray-900">{comment.author}</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      comment.role === 'Agent' 
                        ? 'bg-blue-100 text-blue-800' 
                        : 'bg-gray-100 text-gray-800'
                    }`}>
                      {comment.role}
                    </span>
                  </div>
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <Calendar className="w-4 h-4 mr-1" />
                  {comment.timestamp}
                </div>
              </div>

              {/* Comment Content */}
              <div className="mb-3">
                <p className="text-gray-700 whitespace-pre-wrap">{displayContent}</p>
                {hasLongContent && (
                  <button
                    onClick={() => toggleCommentExpansion(comment.id)}
                    className="mt-2 flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium"
                  >
                    {isExpanded ? (
                      <>
                        <ChevronUp className="w-4 h-4 mr-1" />
                        Show Less
                      </>
                    ) : (
                      <>
                        <ChevronDown className="w-4 h-4 mr-1" />
                        Show More
                      </>
                    )}
                  </button>
                )}
              </div>

              {/* Attachments */}
              {comment.attachments && comment.attachments.length > 0 && (
                <div className="border-t border-gray-100 pt-3">
                  <div className="flex items-center mb-2">
                    <Paperclip className="w-4 h-4 mr-2 text-gray-500" />
                    <span className="text-sm font-medium text-gray-700">
                      Attachments ({comment.attachments.length})
                    </span>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {comment.attachments.map((attachment) => (
                      <div
                        key={attachment.id}
                        className="flex items-center justify-between p-2 bg-gray-50 rounded border"
                      >
                        <div className="flex items-center space-x-2 flex-1 min-w-0">
                          <span className="text-lg">{getFileIcon(attachment.type)}</span>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-gray-900 truncate">
                              {attachment.name}
                            </p>
                            <p className="text-xs text-gray-500">{attachment.size}</p>
                          </div>
                        </div>
                        <div className="flex space-x-1">
                          <button
                            onClick={() => downloadAttachment(attachment)}
                            className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                            title="Download"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                          {attachment.type.startsWith('image/') && (
                            <button
                              className="p-1 text-gray-500 hover:text-blue-600 transition-colors"
                              title="Preview"
                            >
                              <Eye className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default CommentSection;
